<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title default="Starlight Map" suffix="">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
    <style>
      /* Full-screen map styles */
      html, body {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
      
      #map-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw;
        height: 100vh;
      }
      
      /* Hide any potential scrollbars */
      ::-webkit-scrollbar {
        display: none;
      }
      
      /* Ensure MapLibre controls are properly positioned */
      .maplibregl-ctrl-top-right {
        top: 10px;
        right: 10px;
      }
      
      .maplibregl-ctrl-bottom-left {
        bottom: 10px;
        left: 10px;
      }
    </style>
  </head>
  <body class="h-full m-0 p-0 overflow-hidden">
    <main class="h-full">
      <%= @inner_content %>
    </main>
  </body>
</html>

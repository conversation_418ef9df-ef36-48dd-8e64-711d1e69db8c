defmodule StarlightWeb.MapLive do
  @moduledoc """
  LiveView for displaying a full-screen interactive map using MapLibre GL JS.

  This LiveView provides a full-screen mapping interface suitable for GIS applications,
  with bidirectional communication between the LiveView and MapLibre GL JS through
  JavaScript hooks.
  """
  use StarlightWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    # Default map configuration - can be customized based on application needs
    default_config = %{
      center: %{lng: -74.006, lat: 40.7128}, # New York City
      zoom: 10,
      style: "https://demotiles.maplibre.org/style.json",
      min_zoom: 0,
      max_zoom: 22,
      pitch: 0,
      bearing: 0
    }

    socket =
      socket
      |> assign(:page_title, "Interactive Map")
      |> assign(:map_center, default_config.center)
      |> assign(:map_zoom, default_config.zoom)
      |> assign(:map_style, default_config.style)
      |> assign(:map_config, default_config)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      id="map-container"
      class="w-full h-full"
      phx-hook="MapLibre"
      data-center={Jason.encode!(@map_center)}
      data-zoom={@map_zoom}
      data-style={@map_style}
      data-config={Jason.encode!(@map_config)}
    >
      <!-- Map will be rendered here by MapLibre GL JS -->
    </div>
    """
  end

  @impl true
  def handle_event("map_moved", %{"center" => center, "zoom" => zoom}, socket) do
    socket =
      socket
      |> assign(:map_center, center)
      |> assign(:map_zoom, zoom)

    {:noreply, socket}
  end

  @impl true
  def handle_event("map_clicked", %{"lngLat" => lng_lat}, socket) do
    # Handle map click events - can be extended for specific functionality
    IO.inspect(lng_lat, label: "Map clicked at")
    {:noreply, socket}
  end
end

@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */

/* Full-screen map utilities */
.map-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Ensure MapLibre GL controls are accessible */
.maplibregl-ctrl-group {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  border-radius: 6px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.maplibregl-ctrl-group button {
  background: transparent !important;
  border: none !important;
}

.maplibregl-ctrl-group button:hover {
  background: rgba(0, 0, 0, 0.05) !important;
}

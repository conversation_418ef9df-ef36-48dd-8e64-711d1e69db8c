// Simple test hook to verify the hook system works
const MapLibre = {
  mounted() {
    console.log('MapLibre hook mounted successfully!');

    // Add a temporary visual indicator
    this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; font-family: Arial, sans-serif; font-size: 18px;">✅ MapLibre Hook is Working!<br><small>Hook system is functional</small></div>';

    // Test basic functionality
    setTimeout(() => {
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e3f2fd; border: 2px solid #2196f3; color: #1565c0; font-family: Arial, sans-serif; font-size: 18px;">🔄 Hook Updated Successfully!<br><small>Ready to load MapLibre GL JS</small></div>';
    }, 2000);
  },

  updated() {
    console.log('MapLibre hook updated');
  },

  destroyed() {
    console.log('MapLibre hook destroyed');
  }
};

export default MapLibre;

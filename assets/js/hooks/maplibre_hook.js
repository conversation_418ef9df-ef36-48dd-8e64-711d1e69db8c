// Test if basic hook works first
const MapLibre = {
  mounted() {
    console.log('MapLibre hook mounted successfully!');

    // Add a temporary visual indicator
    this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; font-family: Arial, sans-serif;">✅ MapLibre Hook Loaded Successfully!<br><small>Attempting to load MapLibre GL JS...</small></div>';

    // Try to import MapLibre dynamically to catch any import errors
    this.loadMapLibre();
  },

  async loadMapLibre() {
    try {
      const maplibregl = await import('maplibre-gl');
      console.log('MapLibre GL imported successfully:', maplibregl.default.version);

      // Update the display
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e3f2fd; border: 2px solid #2196f3; color: #1565c0; font-family: Arial, sans-serif;">🗺️ MapLibre GL JS Loaded!<br><small>Version: ' + maplibregl.default.version + '</small><br><small>Initializing map...</small></div>';

      // Delay map initialization to ensure DOM is ready
      setTimeout(() => {
        this.initializeMap(maplibregl.default);
      }, 500);

    } catch (error) {
      console.error('Error loading MapLibre GL:', error);
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ Error loading MapLibre GL JS<br><small>' + error.message + '</small></div>';
    }
  },

  initializeMap(maplibregl) {
    try {
      console.log('Initializing MapLibre map...');
      console.log('MapLibre GL version:', maplibregl.version);

      // Get configuration from data attributes
      const center = JSON.parse(this.el.dataset.center);
      const zoom = parseFloat(this.el.dataset.zoom);
      const style = this.el.dataset.style;
      const config = JSON.parse(this.el.dataset.config || '{}');

      console.log('Map config:', { center, zoom, style, config });

      // Check if MapLibre GL is supported
      if (!maplibregl.supported()) {
        console.error('MapLibre GL is not supported in this browser');
        this.el.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">MapLibre GL is not supported in this browser</div>';
        return;
      }

      // Clear the loading message
      this.el.innerHTML = '';

    // Initialize the map with enhanced configuration
    this.map = new maplibregl.Map({
      container: this.el,
      style: style,
      center: [center.lng, center.lat],
      zoom: zoom,
      minZoom: config.min_zoom || 0,
      maxZoom: config.max_zoom || 22,
      pitch: config.pitch || 0,
      bearing: config.bearing || 0,
      attributionControl: true,
      logoPosition: 'bottom-left',
      antialias: true,
      optimizeForTerrain: true
    });

    // Add navigation controls
    this.map.addControl(new maplibregl.NavigationControl(), 'top-right');

    // Add scale control
    this.map.addControl(new maplibregl.ScaleControl({
      maxWidth: 100,
      unit: 'metric'
    }), 'bottom-left');

    // Handle map events and send to LiveView
    this.map.on('moveend', () => {
      const center = this.map.getCenter();
      const zoom = this.map.getZoom();
      
      this.pushEvent('map_moved', {
        center: { lng: center.lng, lat: center.lat },
        zoom: zoom
      });
    });

    this.map.on('click', (e) => {
      this.pushEvent('map_clicked', {
        lngLat: { lng: e.lngLat.lng, lat: e.lngLat.lat }
      });
    });

    // Handle map load event
    this.map.on('load', () => {
      console.log('MapLibre GL JS map loaded successfully');
    });

    // Handle map errors
    this.map.on('error', (e) => {
      console.error('MapLibre GL JS error:', e);
    });

    } catch (error) {
      console.error('Error initializing MapLibre map:', error);
    }
  },

  updated() {
    // Handle updates from LiveView if needed
    if (this.map) {
      const center = JSON.parse(this.el.dataset.center);
      const zoom = parseFloat(this.el.dataset.zoom);
      
      // Only update if the center or zoom has changed significantly
      const currentCenter = this.map.getCenter();
      const currentZoom = this.map.getZoom();
      
      const centerChanged = Math.abs(currentCenter.lng - center.lng) > 0.001 || 
                           Math.abs(currentCenter.lat - center.lat) > 0.001;
      const zoomChanged = Math.abs(currentZoom - zoom) > 0.1;
      
      if (centerChanged || zoomChanged) {
        this.map.easeTo({
          center: [center.lng, center.lat],
          zoom: zoom,
          duration: 1000
        });
      }
    }
  },

  destroyed() {
    // Clean up the map when the LiveView is destroyed
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  }
};

export default MapLibre;

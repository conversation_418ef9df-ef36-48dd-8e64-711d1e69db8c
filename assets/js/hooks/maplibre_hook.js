import maplibregl from 'maplibre-gl';

/**
 * MapLibre GL JS Hook for Phoenix LiveView
 * 
 * This hook handles the initialization and management of MapLibre GL JS maps
 * within Phoenix LiveView, providing bidirectional communication between
 * the map and the LiveView process.
 */
const MapLibre = {
  mounted() {
    // Get configuration from data attributes
    const center = JSON.parse(this.el.dataset.center);
    const zoom = parseFloat(this.el.dataset.zoom);
    const style = this.el.dataset.style;
    const config = JSON.parse(this.el.dataset.config || '{}');

    // Initialize the map with enhanced configuration
    this.map = new maplibregl.Map({
      container: this.el,
      style: style,
      center: [center.lng, center.lat],
      zoom: zoom,
      minZoom: config.min_zoom || 0,
      maxZoom: config.max_zoom || 22,
      pitch: config.pitch || 0,
      bearing: config.bearing || 0,
      attributionControl: true,
      logoPosition: 'bottom-left',
      antialias: true,
      optimizeForTerrain: true
    });

    // Add navigation controls
    this.map.addControl(new maplibregl.NavigationControl(), 'top-right');

    // Add scale control
    this.map.addControl(new maplibregl.ScaleControl({
      maxWidth: 100,
      unit: 'metric'
    }), 'bottom-left');

    // Handle map events and send to LiveView
    this.map.on('moveend', () => {
      const center = this.map.getCenter();
      const zoom = this.map.getZoom();
      
      this.pushEvent('map_moved', {
        center: { lng: center.lng, lat: center.lat },
        zoom: zoom
      });
    });

    this.map.on('click', (e) => {
      this.pushEvent('map_clicked', {
        lngLat: { lng: e.lngLat.lng, lat: e.lngLat.lat }
      });
    });

    // Handle map load event
    this.map.on('load', () => {
      console.log('MapLibre GL JS map loaded successfully');
    });

    // Handle map errors
    this.map.on('error', (e) => {
      console.error('MapLibre GL JS error:', e);
    });
  },

  updated() {
    // Handle updates from LiveView if needed
    if (this.map) {
      const center = JSON.parse(this.el.dataset.center);
      const zoom = parseFloat(this.el.dataset.zoom);
      
      // Only update if the center or zoom has changed significantly
      const currentCenter = this.map.getCenter();
      const currentZoom = this.map.getZoom();
      
      const centerChanged = Math.abs(currentCenter.lng - center.lng) > 0.001 || 
                           Math.abs(currentCenter.lat - center.lat) > 0.001;
      const zoomChanged = Math.abs(currentZoom - zoom) > 0.1;
      
      if (centerChanged || zoomChanged) {
        this.map.easeTo({
          center: [center.lng, center.lat],
          zoom: zoom,
          duration: 1000
        });
      }
    }
  },

  destroyed() {
    // Clean up the map when the LiveView is destroyed
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  }
};

export default MapLibre;

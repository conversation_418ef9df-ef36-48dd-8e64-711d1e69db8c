// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"

// Import MapLibre GL JS CSS
import 'maplibre-gl/dist/maplibre-gl.css'

// Import MapLibre GL JS
import * as maplibregl from 'maplibre-gl';

// Define hooks directly to avoid import issues
let Hooks = {
  MapLibre: {
    mounted() {
      console.log('MapLibre hook mounted successfully!');

      // Prevent double initialization
      if (this.map) {
        console.log('Map already initialized, skipping...');
        return;
      }

      // Show loading message
      this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #e8f5e8; border: 2px solid #4caf50; color: #2e7d32; font-family: Arial, sans-serif; font-size: 18px;">✅ MapLibre Hook Loaded!<br><small>Initializing map...</small></div>';

      // Initialize map after a short delay
      setTimeout(() => {
        this.initializeMap();
      }, 500);
    },

    initializeMap() {
      try {
        // Prevent double initialization
        if (this.map) {
          console.log('Map already exists, skipping initialization...');
          return;
        }

        // Get the correct MapLibre object (might be default export or named export)
        const MapLibreGL = maplibregl.default || maplibregl;
        console.log('Using MapLibre GL:', MapLibreGL);

        // Try to get version from different possible locations
        const version = MapLibreGL.version || maplibregl.getVersion?.() || 'unknown';
        console.log('MapLibre GL version:', version);

        // Get configuration from data attributes
        const center = JSON.parse(this.el.dataset.center);
        const zoom = parseFloat(this.el.dataset.zoom);
        const style = this.el.dataset.style;

        console.log('Map config:', { center, zoom, style });

        // Check if MapLibre GL is supported (skip if function doesn't exist)
        if (typeof MapLibreGL.supported === 'function' && !MapLibreGL.supported()) {
          console.error('MapLibre GL is not supported in this browser');
          this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ MapLibre GL is not supported in this browser</div>';
          return;
        }

        // Clear the loading message
        this.el.innerHTML = '';

        // Initialize the map
        this.map = new MapLibreGL.Map({
          container: this.el,
          style: style,
          center: [center.lng, center.lat],
          zoom: zoom,
          attributionControl: true,
          logoPosition: 'bottom-left'
        });

        // Add navigation controls
        this.map.addControl(new MapLibreGL.NavigationControl(), 'top-right');

        // Handle map load event
        this.map.on('load', () => {
          console.log('MapLibre GL JS map loaded successfully');
        });

        // Handle map errors
        this.map.on('error', (e) => {
          console.error('MapLibre GL JS error:', e);
        });

      } catch (error) {
        console.error('Error initializing MapLibre map:', error);
        this.el.innerHTML = '<div style="padding: 20px; text-align: center; background: #ffebee; border: 2px solid #f44336; color: #c62828; font-family: Arial, sans-serif;">❌ Error: ' + error.message + '</div>';
      }
    },

    updated() {
      console.log('MapLibre hook updated');
    },

    destroyed() {
      console.log('MapLibre hook destroyed');
      if (this.map) {
        this.map.remove();
        this.map = null;
      }
    }
  }
}

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

